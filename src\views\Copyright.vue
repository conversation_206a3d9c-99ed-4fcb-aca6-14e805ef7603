<template>
  <div class="copyright">
    <div class="container">
      <div class="copyright-content">
        <h1>版权说明</h1>
        
        <section>
          <h2>1、本网站申明</h2>
          <p>本网站是一个网盘搜索引擎网站，全站链接通过蜘蛛程序收集自百度网盘公开链接，以非人工方式自动生成页面。本网站本身不储存、复制、传播、控制编辑任何网盘资源文件，不提供下载服务，仅做索引并提供搜索功能。本站所有功能服务仅供学习交流，如果你喜欢，请购买正版。</p>
        </section>

        <section>
          <h2>2、相关法律：避风港原则</h2>
          <p>《条例》第14条规定："对提供信息存储空间或者提供搜索、链接服务的网络服务提供者，权利人认为其服务所涉及的作品、表演、录音录像制品，侵犯自己的信息网络传播权或者被删除、改变了自己的权利管理电子信息的，可以向该网络服务提供者提交书面通知，要求网络服务提供者删除该作品、表演、录音录像制品，或者断开与该作品、表演、录音录像制品的链接。</p>
          
          <h3>通知书应当包含下列内容：</h3>
          <ul>
            <li>（一）权利人的姓名（名称）、联系方式和地址；</li>
            <li>（二）要求删除或者断开链接的侵权作品、表演、录音录像制品的名称和网络地址；</li>
            <li>（三）构成侵权的初步证明材料。</li>
          </ul>
          <p><strong>权利人应当对通知书的真实性负责。</strong></p>
        </section>

        <section>
          <h2>3、著作权保护声明</h2>
          <p>具体措施和步骤如下：</p>
          
          <h3>权利请求</h3>
          <p>任何个人或单位如果同时符合以下两个条件：</p>
          <ol>
            <li>是某一作品的著作权人和/或依法可以行使信息网络传播权的权利人；</li>
            <li>本网站通过蜘蛛程序收集自百度网盘公开分享链接侵犯了上述作品信息的权利。</li>
          </ol>
          <p>上述个人或单位可以以电子邮件的通讯方式向本网站提交权利通知。</p>
          
          <h3>权利通知必须包含如下资料：</h3>
          <ol>
            <li><strong>联络信息：</strong>请提供具体的联络信息，包括姓名、身份证或护照复印件（对自然人）、营业执照复印件（对单位）、通信地址、电话号码、传真和电子邮件。</li>
            <li><strong>侵权作品信息：</strong>请完整、准确地指明涉嫌侵权作品的名称和登载该作品的网页的地址。</li>
            <li><strong>初步证明材料：</strong>请提供构成侵权的初步证明材料，谨此提示如以下材料可能会构成初步证明：
              <ul>
                <li>a. 对涉嫌侵权作品拥有著作权和／或依法可以行使信息网络传播权的权属证明；</li>
                <li>b. 对涉嫌侵权作品侵权事实的举证。</li>
              </ul>
            </li>
            <li><strong>签名确认：</strong>请您在该权利通知落款处亲笔签名，如果您是依法成立的机构或组织，请您加盖公章。</li>
          </ol>
          
          <div class="warning-box">
            <p><strong>⚠️ 重要提示：</strong>如果权利通知的陈述失实，权利通知提交者将承担由此造成的全部法律责任（包括但不限于赔偿各种费用及律师费）</p>
          </div>
        </section>

        <section>
          <h2>4、投诉邮箱</h2>
          <div class="contact-box">
            <p><strong>版权投诉邮箱：</strong><span class="email"><EMAIL></span></p>
            <p class="note">请在邮件主题中注明"版权投诉"字样，以便我们及时处理您的请求。</p>
          </div>
        </section>

        <section>
          <h2>5、本网站义务</h2>
          <p>本网站作为搜索引擎，将严格拥护著作权拥有者的权利，如果百度网盘、阿里云盘、夸克网盘等方未能及时受理你的投诉建议，烦请您根据第三条提供有效书面证明给我们，我们经过确认后认为侵权，将立即屏蔽删除该索引内容或断开该文件链接，以免对您造成损失。</p>
        </section>

        <section>
          <h2>6、建议说明</h2>
          <p>由于网站内容为机器自动收集，本网站毫无侵权的主观意愿。我们一直在致力于避免侵权行为，除了积极配合权利方的要求，另外还定期自我巡查，主动删除屏蔽认为有涉及版权的链接。但由于数据量大，系统屏蔽机制结合人工审查都难免存在疏漏。</p>
          <p>这里建议维权者也能本着实事求是的态度，确保内容确实是属于您的，避免因为同名、空内容等对本网站错误指责，特此建议。</p>
        </section>

        <section>
          <h2>7、处理流程</h2>
          <div class="process-box">
            <ol>
              <li><strong>接收投诉：</strong>我们将在收到有效投诉后24小时内确认收到</li>
              <li><strong>审核材料：</strong>我们将在3个工作日内审核您提供的证明材料</li>
              <li><strong>处理结果：</strong>经确认侵权后，我们将立即删除相关链接并通知您处理结果</li>
              <li><strong>后续跟进：</strong>如有疑问，您可以随时联系我们进行沟通</li>
            </ol>
          </div>
        </section>

        <div class="back-button">
          <button @click="goBack" class="btn-back">返回首页</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.copyright {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40px 0;
}

.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.copyright-content {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  line-height: 1.6;
}

h1 {
  color: #8e44ad;
  text-align: center;
  margin-bottom: 30px;
  font-size: 2.2rem;
  font-weight: 600;
}

h2 {
  color: #2c3e50;
  margin: 30px 0 15px 0;
  font-size: 1.4rem;
  font-weight: 600;
  border-bottom: 2px solid #8e44ad;
  padding-bottom: 8px;
}

h3 {
  color: #34495e;
  margin: 25px 0 12px 0;
  font-size: 1.2rem;
  font-weight: 500;
}

p {
  color: #555;
  margin-bottom: 15px;
  text-align: justify;
}

ul, ol {
  margin: 15px 0;
  padding-left: 20px;
}

li {
  color: #555;
  margin-bottom: 8px;
}

section {
  margin-bottom: 35px;
}

.warning-box {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-left: 4px solid #f39c12;
  padding: 15px;
  margin: 20px 0;
  border-radius: 4px;
}

.warning-box p {
  margin: 0;
  color: #856404;
  font-weight: 500;
}

.contact-box {
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-left: 4px solid #27ae60;
  padding: 20px;
  margin: 20px 0;
  border-radius: 4px;
}

.contact-box p {
  margin: 5px 0;
  color: #2d5a2d;
}

.email {
  font-weight: 600;
  color: #27ae60;
  font-family: monospace;
  font-size: 1.1rem;
}

.note {
  font-size: 0.9rem;
  color: #666 !important;
  font-style: italic;
}

.process-box {
  background: #f8f9ff;
  border: 1px solid #d1d9ff;
  border-left: 4px solid #8e44ad;
  padding: 20px;
  margin: 20px 0;
  border-radius: 4px;
}

.process-box ol {
  margin: 0;
}

.process-box li {
  color: #4a4a4a;
  margin-bottom: 10px;
}

.back-button {
  text-align: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e9ecef;
}

.btn-back {
  background: #8e44ad;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-back:hover {
  background: #7d3c98;
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .copyright-content {
    padding: 25px 20px;
  }
  
  h1 {
    font-size: 1.8rem;
  }
  
  h2 {
    font-size: 1.2rem;
  }
  
  h3 {
    font-size: 1.1rem;
  }
  
  .warning-box,
  .contact-box,
  .process-box {
    padding: 15px;
  }
}
</style>
