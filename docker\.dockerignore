# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist
build

# 开发工具
.vscode
.idea
*.swp
*.swo

# 版本控制
.git
.gitignore
.gitattributes

# 环境文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# 依赖锁定文件（保留package-lock.json）
yarn.lock

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 微任务缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# parcel-bundler缓存
.cache
.parcel-cache

# next.js构建输出
.next

# nuxt.js构建输出
.nuxt

# vuepress构建输出
.vuepress/dist

# Serverless目录
.serverless

# FuseBox缓存
.fusebox/

# DynamoDB本地文件
.dynamodb/

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# 文档
README.md
CHANGELOG.md
LICENSE

# 部署脚本
deploy.sh
docker-compose.yml

# 测试文件
tests/
test/
__tests__/
*.test.js
*.spec.js

# Docker相关文件
docker/
