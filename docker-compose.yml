version: '3.8'

services:
  # 前端应用
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    image: resource-search-frontend:latest
    container_name: resource-search-frontend
    restart: unless-stopped
    ports:
      - "80:3000"
    volumes:
      - /etc/localtime:/etc/localtime:ro
    environment:
      - NODE_ENV=production
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx反向代理（可选，如果需要更复杂的代理配置）
  # nginx-proxy:
  #   image: nginx:alpine
  #   container_name: nginx-proxy
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - frontend
  #   networks:
  #     - app-network

networks:
  app-network:
    driver: bridge

# 可选：添加数据卷用于持久化
# volumes:
#   app-data:
#     driver: local
