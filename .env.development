# 开发环境配置
VITE_APP_TITLE=资源搜索平台
VITE_APP_BASE_API=116.205.244.106:9090/api

# 是否使用真实API（设置为true时使用真实API，否则使用模拟数据）
VITE_USE_REAL_API=true

# 图片代理配置
# backend: 使用后端代理（推荐）
# imageproxy: 使用 images.weserv.nl 代理服务
# allorigins: 使用 allorigins 代理服务
# cors-anywhere: 使用 cors-anywhere 代理服务（需要激活）
VITE_USE_IMAGE_PROXY=imageproxy

# 图片适配模式
# smart: 智能模式（竖屏用contain，横屏用cover）
# cover: 覆盖模式（填满容器，可能裁剪）
# contain: 包含模式（完整显示，可能有空白）
# auto: 自动模式（根据极端比例智能选择）
VITE_IMAGE_FIT_MODE=smart

# 卡片尺寸配置
# compact: 紧凑型（更瘦更小）
# normal: 正常型（默认尺寸）
# large: 大型（更大更宽）
VITE_CARD_SIZE=compact
